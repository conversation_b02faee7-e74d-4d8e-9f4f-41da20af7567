const express = require('express')
const path = require('path')
const app = express()
const Joi = require('joi')
const fs = require('fs')
const https = require('https')
const helmet = require('helmet')
const rateLimit = require('express-rate-limit')

// 加载环境变量
require('dotenv').config()

// 检查必要的环境变量
if (!process.env.JWT_SECRET) {
  console.error('错误: JWT_SECRET 环境变量未设置')
  process.exit(1)
}

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试',
  standardHeaders: true,
  legacyHeaders: false,
})
app.use(limiter)



const cors = require('cors')
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://127.0.0.1:5173',
    'http://localhost:5174',
    'http://127.0.0.1:5174',
    'http://************:3001',
    'https://gamesbe.asia:3001',
    'https://games.hkbu.tech',
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
}))



app.use(express.urlencoded({ extended: false }))
app.use(express.json())

// 安全的静态文件服务，用于访问上传的游戏文件
app.use('/upload', (req, res, next) => {
  // 设置安全头
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'SAMEORIGIN')

  // 检查文件路径，防止路径遍历攻击
  const requestedPath = req.path
  if (requestedPath.includes('..') || requestedPath.includes('~')) {
    return res.status(403).send('Forbidden')
  }

  next()
})
app.use('/upload', express.static(path.join(__dirname, 'upload')))

app.use(function(req,res,next){
  res.cc = function(err, status = 1) {
    res.send({
      status,
      message: err instanceof Error ? err.message : err
    })
  }
  next()
})

const expressJWT = require('express-jwt')

app.use(expressJWT({
  secret: process.env.JWT_SECRET,
  algorithms: ['HS256'] }).unless({ path: [/^\/api\/login/, /^\/api\/gameDesc/, /^\/api\/game/, /^\/api\/AccessLessons/, ] } ))
  // /^\/register/, /^\/upload\/game/

// 错误处理中间件必须放在最后
app.use(function(err, req, res, next){
  if(err instanceof Joi.ValidationError) return res.cc(err)
  if(err.name === 'UnauthorizedError') return res.cc('身份认证失败')
  res.cc(err)
})

const userRouter = require('./router/user.js')
const gameRouter = require('./router/games.js')
const playGameRouter = require('./router/playGame.js')
// const remoteUpload = require('./router/remoteUpload.js')
app.use('/api',userRouter)
app.use('/api',gameRouter)
// app.use('/upload',remoteUpload)
app.use('/api/AccessLessons', playGameRouter)

// const options = {
//   key: fs.readFileSync(path.join(__dirname, 'ssl/buGames_backend_1751958371/Nginx/privkey.key')),
//   cert: fs.readFileSync(path.join(__dirname, 'ssl/buGames_backend_1751958371/Nginx/fullchain.pem'))
// }


// https.createServer(options, app).listen(3001, '0.0.0.0', () => {
//   console.log('HTTPS Server started')
// })


app.listen(3001, () => {
  console.log('Server started')
})
