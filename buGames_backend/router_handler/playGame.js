const fs = require('fs')
const path = require('path')
const db = require('../db')
const { sanitizeHTML } = require('../utils/security')

const playGame = (req, res) => {
  const game_name = req.params.game_name

  if (!game_name) {
    return res.cc('Missing necessary parameter game_name')
  }

  // URL decode parameters in case they contain encoded characters.
  const decodedGameName = decodeURIComponent(game_name)

  // Query by game_name and verify game_name matches for security.
  const sql = 'select * from games where game_name = ?'

  db.query(sql, [decodedGameName], (err, results) => {
    if(err) {
      return res.cc(err)
    }

    if(results.length === 0) {
      return res.cc('Game does not exist')
    }

    const filePath = results[0].file_path
    // Build the full file path
    const fullPath = path.join(__dirname, '..', filePath)

    if (!fs.existsSync(fullPath)) {
      return res.cc('Game file does not exist')
    }

    // 验证文件路径安全性，防止路径遍历攻击
    const normalizedPath = path.normalize(fullPath)
    const uploadDir = path.join(__dirname, '..', 'upload')
    if (!normalizedPath.startsWith(uploadDir)) {
      return res.cc('Invalid file path')
    }

    try {
      const text = fs.readFileSync(fullPath, 'utf8')

      // 设置安全响应头
      res.setHeader('Content-Type', 'text/html; charset=utf-8')
      res.setHeader('X-Content-Type-Options', 'nosniff')
      res.setHeader('X-Frame-Options', 'SAMEORIGIN')
      res.setHeader('X-XSS-Protection', '1; mode=block')

      // 注意：这里不对游戏文件内容进行过度清理，因为游戏需要执行脚本
      // 但我们已经在上传时进行了基本的安全清理
      res.send(text)
    } catch (error) {
      console.error('Error reading game file:', error)
      return res.cc('Failed to load game file')
    }
  })
}

module.exports = {
  playGame
}
