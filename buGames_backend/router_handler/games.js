const db = require('../db')
const fs = require('fs')
const path = require('path')
const { sanitizeHTML, sanitizeGameFile, escapeHTML, isSecureFilename, generateSecureFilename } = require('../utils/security')

const getGameList = (req, res) => {
  const sql = 'select * from games'
  db.query(sql, (err, results) => {
    if(err) return res.cc(err)
    res.send({
      status: 0,
      message: '获取游戏列表成功',
      data: results
    })
  })
}

const getGameDesc = (req, res) => {
  // 获取game_id，优先从URL参数获取，其次从查询参数获取
  const game_id = req.params.game_id || req.query.game_id

  // 验证game_id是否存在
  if (!game_id) {
    return res.cc('缺少必要参数game_id')
  }

  // 验证game_id是否为有效数字
  if (isNaN(game_id) || game_id <= 0) {
    return res.cc('game_id必须为有效的正整数')
  }

  const sql = 'select * from games where game_id = ?'
  db.query(sql, game_id, (err, results) => {
    if(err) return res.cc(err)
    if(results.length === 0) return res.cc('游戏不存在')

    // 对游戏描述进行HTML转义，防止XSS
    const gameData = { ...results[0] }
    if (gameData.game_desc) {
      gameData.game_desc = escapeHTML(gameData.game_desc)
    }
    if (gameData.game_name) {
      gameData.game_name = escapeHTML(gameData.game_name)
    }

    res.send({
      status: 0,
      message: '获取游戏描述成功',
      data: gameData
    })
  })
}

const addNewGame = (req, res) => {
  // 验证必要参数
  const { game_name, game_desc } = req.body

  // 验证文件名安全性
  if (!req.file || !isSecureFilename(req.file.originalname)) {
    return res.cc('文件名包含不安全字符')
  }

  // 检查游戏名是否已存在
  const sql1 = 'select * from games where game_name = ?'
  db.query(sql1, game_name, (err, results) => {
    if(err) return res.cc(err)
    if(results.length !== 0) return res.cc('游戏名已存在')

    // 创建游戏目录
    const gameDir = path.join(__dirname, '../upload', game_name)

    try {
      // 如果目录不存在则创建
      if (!fs.existsSync(gameDir)) {
        fs.mkdirSync(gameDir, { recursive: true })
      }

      // 生成安全的文件名
      const secureFileName = generateSecureFilename(req.file.originalname)
      const filePath = path.join(gameDir, secureFileName)
      const relativePath = `upload/${game_name}/${secureFileName}`

      // 读取文件内容并进行温和的安全处理
      const originalContent = fs.readFileSync(req.file.path, 'utf8')

      // 使用专门的游戏文件安全处理函数
      const cleanContent = sanitizeGameFile(originalContent)

      // 写入处理后的内容
      fs.writeFileSync(filePath, cleanContent, 'utf8')

      // 删除临时文件
      fs.unlinkSync(req.file.path)

      // 插入数据库 - 对用户输入进行转义
      const sql2 = 'insert into games (game_name, game_desc, file_path) values (?, ?, ?)'
      const safeGameDesc = game_desc ? escapeHTML(game_desc) : ''
      db.query(sql2, [game_name, safeGameDesc, relativePath], (err, results) => {
        if(err) {
          // 如果数据库插入失败，删除已上传的文件
          try {
            fs.unlinkSync(filePath)
            fs.rmdirSync(gameDir)
          } catch (cleanupErr) {
            console.error('清理文件失败:', cleanupErr)
          }
          return res.cc(err)
        }

        if(results.affectedRows !== 1) {
          // 如果插入失败，删除已上传的文件
          try {
            fs.unlinkSync(filePath)
            fs.rmdirSync(gameDir)
          } catch (cleanupErr) {
            console.error('清理文件失败:', cleanupErr)
          }
          return res.cc('添加游戏失败')
        }

        res.send({
          status: 0,
          message: '添加游戏成功',
          data: {
            game_id: results.insertId,
            game_name: game_name,
            game_desc: game_desc || '',
            file_path: relativePath
          }
        })
      })

    } catch (fileErr) {
      console.error('文件操作失败:', fileErr)
      return res.cc('文件保存失败')
    }
  })
}

const editGame = (req, res) => {

  const { game_name, game_desc, game_id } = req.body

  if (!game_id) {
    return res.cc('缺少游戏ID')
  }

  if (!game_name) {
    return res.cc('游戏名称不能为空')
  }

  // 如果有文件上传，验证文件名安全性
  if (req.file && !isSecureFilename(req.file.originalname)) {
    return res.cc('文件名包含不安全字符')
  }

  // 首先获取原游戏信息
  const getGameSql = 'select * from games where game_id = ?'
  db.query(getGameSql, game_id, (err, gameResults) => {
    if(err) return res.cc(err)
    if(gameResults.length === 0) return res.cc('游戏不存在')

    const originalGame = gameResults[0]
    let newFilePath = originalGame.file_path
    let oldGameDir = null
    let newGameDir = null

    // 检查游戏名是否被其他游戏使用
    const checkNameSql = 'select * from games where game_name = ? and game_id != ?'
    db.query(checkNameSql, [game_name, game_id], (err, nameResults) => {
      if(err) return res.cc(err)
      if(nameResults.length > 0) return res.cc('游戏名已被其他游戏使用')

      try {
        // 如果有新文件上传
        if (req.file) {
          // 创建新的游戏目录
          newGameDir = path.join(__dirname, '../upload', game_name)

          if (!fs.existsSync(newGameDir)) {
            fs.mkdirSync(newGameDir, { recursive: true })
          }

          // 生成安全的文件名
          const secureFileName = generateSecureFilename(req.file.originalname)
          const filePath = path.join(newGameDir, secureFileName)
          newFilePath = `upload/${game_name}/${secureFileName}`

          // 读取文件内容并进行温和的安全处理
          const originalContent = fs.readFileSync(req.file.path, 'utf8')

          // 使用专门的游戏文件安全处理函数
          const cleanContent = sanitizeGameFile(originalContent)

          // 写入处理后的内容
          fs.writeFileSync(filePath, cleanContent, 'utf8')

          // 删除临时文件
          fs.unlinkSync(req.file.path)
        } else if (game_name !== originalGame.game_name) {
          // 如果没有新文件但游戏名改变了，需要移动现有文件
          oldGameDir = path.join(__dirname, '../upload', originalGame.game_name)
          newGameDir = path.join(__dirname, '../upload', game_name)

          if (fs.existsSync(oldGameDir)) {
            // 创建新目录
            if (!fs.existsSync(newGameDir)) {
              fs.mkdirSync(newGameDir, { recursive: true })
            }

            // 移动文件
            const fileName = path.basename(originalGame.file_path)
            const oldFilePath = path.join(oldGameDir, fileName)
            const newFilePathFull = path.join(newGameDir, fileName)

            if (fs.existsSync(oldFilePath)) {
              fs.renameSync(oldFilePath, newFilePathFull)
              newFilePath = `upload/${game_name}/${fileName}`

              // 删除旧目录（如果为空）
              try {
                fs.rmdirSync(oldGameDir)
              } catch (e) {
                // 目录不为空或其他错误，忽略
                console.log('无法删除旧目录:', e.message)
              }
            }
          }
        }

        // 更新数据库 - 对用户输入进行转义
        const updateSql = 'update games set game_desc = ?, file_path = ?, game_name = ? where game_id = ?'
        const safeGameDesc = game_desc ? escapeHTML(game_desc) : ''
        db.query(updateSql, [safeGameDesc, newFilePath, game_name, game_id], (err, results) => {
          if(err) {
            // 如果数据库更新失败，需要回滚文件操作
            if (req.file && newGameDir) {
              try {
                fs.unlinkSync(path.join(newGameDir, req.file.originalname))
                fs.rmdirSync(newGameDir)
              } catch (cleanupErr) {
                console.error('清理文件失败:', cleanupErr)
              }
            }
            return res.cc(err)
          }

          if(results.affectedRows !== 1) {
            // 如果更新失败，需要回滚文件操作
            if (req.file && newGameDir) {
              try {
                fs.unlinkSync(path.join(newGameDir, req.file.originalname))
                fs.rmdirSync(newGameDir)
              } catch (cleanupErr) {
                console.error('清理文件失败:', cleanupErr)
              }
            }
            return res.cc('编辑游戏失败')
          }

          res.send({
            status: 0,
            message: '编辑游戏成功',
            data: {
              game_id: game_id,
              game_name: game_name,
              game_desc: game_desc || '',
              file_path: newFilePath
            }
          })
        })

      } catch (fileErr) {
        console.error('文件操作失败:', fileErr)
        return res.cc('文件操作失败')
      }
    })
  })
}

const deleteGame = (req, res) => {
  // 获取game_id，优先从URL参数获取，其次从查询参数获取
  const game_id = req.params.game_id || req.query.game_id

  // 验证game_id是否存在
  if (!game_id) {
    return res.cc('缺少必要参数game_id')
  }

  // 验证game_id是否为有效数字
  if (isNaN(game_id) || game_id <= 0) {
    return res.cc('game_id必须为有效的正整数')
  }

  const sql = 'delete from games where game_id = ?'
  db.query(sql, game_id, (err, results) => {
    if(err) return res.cc(err)
    if(results.affectedRows !== 1) return res.cc('删除游戏失败，可能游戏不存在')
    res.send({
      status: 0,
      message: '删除游戏成功'
    })
  })
}


module.exports = {
  getGameList,
  getGameDesc,
  addNewGame,
  editGame,
  deleteGame
}