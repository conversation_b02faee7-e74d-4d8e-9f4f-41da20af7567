<!DOCTYPE html>
<html>
<head>
    <title>Test Game Upload</title>
</head>
<body>
    <h1>Test Game Upload</h1>
    <form action="http://127.0.0.1:3000/newGame" method="post" enctype="multipart/form-data">
        <div>
            <label>Game Name:</label>
            <input type="text" name="game_name" value="TestNumberGame" required>
        </div>
        <div>
            <label>Game Description:</label>
            <textarea name="game_desc">A simple number guessing game for testing interactive features</textarea>
        </div>
        <div>
            <label>Game File:</label>
            <input type="file" name="gameFile" accept=".html" required>
        </div>
        <div>
            <label>Authorization Token:</label>
            <input type="text" name="token" placeholder="Enter your JWT token">
        </div>
        <button type="submit">Upload Game</button>
    </form>

    <script>
        // Add token to Authorization header
        document.querySelector('form').addEventListener('submit', function(e) {
            const token = document.querySelector('input[name="token"]').value;
            if (token) {
                // This is a simple test form, in real app token would be handled properly
                console.log('Token:', token);
            }
        });
    </script>
</body>
</html>
