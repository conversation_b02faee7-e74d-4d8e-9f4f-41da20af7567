const createDOMPurify = require('dompurify')
const { JSDOM } = require('jsdom')

// 创建DOMPurify实例
const window = new JSDOM('').window
const DOMPurify = createDOMPurify(window)

/**
 * 清理HTML内容，防止XSS攻击
 * @param {string} html - 需要清理的HTML内容
 * @param {boolean} allowScripts - 是否允许脚本标签（游戏文件需要）
 * @returns {string} - 清理后的HTML内容
 */
const sanitizeHTML = (html, allowScripts = false) => {
  if (!html || typeof html !== 'string') {
    return ''
  }

  let config = {
    ALLOWED_TAGS: [
      'div', 'span', 'p', 'br', 'strong', 'em', 'u', 'i', 'b',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li',
      'a', 'img',
      'table', 'thead', 'tbody', 'tr', 'td', 'th',
      'canvas', 'svg'
    ],
    ALLOWED_ATTR: [
      'class', 'id', 'style', 'src', 'alt', 'href', 'title',
      'width', 'height', 'data-*'
    ],
    ALLOW_DATA_ATTR: true
  }

  // 如果是游戏文件，允许更多标签和属性
  if (allowScripts) {
    // 对于游戏文件，使用更宽松的配置，只移除明显危险的内容
    config = {
      ALLOWED_TAGS: false, // 允许所有标签
      ALLOWED_ATTR: false, // 允许所有属性
      FORBID_TAGS: ['iframe', 'object', 'embed', 'applet'], // 只禁止这些危险标签
      FORBID_ATTR: ['onerror'], // 只禁止明显的恶意事件
      ALLOW_DATA_ATTR: true,
      ALLOW_UNKNOWN_PROTOCOLS: false,
      SANITIZE_DOM: false // 保持DOM结构
    }
  }

  try {
    return DOMPurify.sanitize(html, config)
  } catch (error) {
    console.error('HTML sanitization error:', error)
    return ''
  }
}

/**
 * 专门为游戏文件设计的最小化安全处理
 * 基本不做修改，只记录处理日志，保留所有游戏功能
 * @param {string} content - 游戏文件内容
 * @returns {string} - 处理后的内容
 */
const sanitizeGameFile = (content) => {
  if (!content || typeof content !== 'string') {
    return ''
  }

  // 对于游戏文件，我们采用最宽松的策略
  // 只做基本的日志记录，几乎不修改内容
  console.log('Processing game file, length:', content.length)

  // 返回原始内容，保留所有游戏功能
  // 包括：onclick, addEventListener, DOM操作, CSS样式等
  return content
}

/**
 * 转义HTML特殊字符
 * @param {string} text - 需要转义的文本
 * @returns {string} - 转义后的文本
 */
const escapeHTML = (text) => {
  if (!text || typeof text !== 'string') {
    return ''
  }
  
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

/**
 * 验证文件名安全性
 * @param {string} filename - 文件名
 * @returns {boolean} - 是否安全
 */
const isSecureFilename = (filename) => {
  if (!filename || typeof filename !== 'string') {
    return false
  }
  
  // 检查危险字符
  const dangerousChars = /[<>:"|?*\x00-\x1f]/
  if (dangerousChars.test(filename)) {
    return false
  }
  
  // 检查路径遍历
  if (filename.includes('..') || filename.includes('~')) {
    return false
  }
  
  // 检查保留名称（Windows）
  const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i
  if (reservedNames.test(filename)) {
    return false
  }
  
  return true
}

/**
 * 生成安全的文件名
 * @param {string} originalName - 原始文件名
 * @returns {string} - 安全的文件名
 */
const generateSecureFilename = (originalName) => {
  if (!originalName || typeof originalName !== 'string') {
    return `file_${Date.now()}.html`
  }
  
  // 提取文件扩展名
  const ext = originalName.split('.').pop() || 'html'
  
  // 清理文件名，只保留安全字符
  const safeName = originalName
    .replace(/[^a-zA-Z0-9._-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '')
  
  // 如果清理后为空，生成默认名称
  if (!safeName || safeName === `.${ext}`) {
    return `file_${Date.now()}.${ext}`
  }
  
  return safeName
}

module.exports = {
  sanitizeHTML,
  sanitizeGameFile,
  escapeHTML,
  isSecureFilename,
  generateSecureFilename
}
