{"name": "bugames_backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node app.js", "test": "echo \"No tests\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@escook/express-joi": "^1.1.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^17.2.0", "express": "^4.17.1", "express-jwt": "^5.3.3", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "fs": "^0.0.1-security", "helmet": "^8.1.0", "joi": "^17.13.3", "jsdom": "^26.1.0", "jsonwebtoken": "^8.5.1", "multer": "^2.0.1", "mysql": "^2.18.1"}}