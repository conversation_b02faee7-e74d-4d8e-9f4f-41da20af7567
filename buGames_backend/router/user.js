const express = require('express')
const rateLimit = require('express-rate-limit')

const router = express.Router()

const { login } = require('../router_handler/user')
// const {  register } = require('../router_handler/user')

const expressJoi = require('@escook/express-joi')
const userSchema = require('../schema/user')

// 登录接口特殊限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 限制每个IP 15分钟内最多5次登录尝试
  message: '登录尝试过于频繁，请15分钟后再试',
  standardHeaders: true,
  legacyHeaders: false,
})

router.post('/login', loginLimiter, expressJoi(userSchema), login)
// router.post('/register', expressJoi(userSchema), register)

module.exports = router
