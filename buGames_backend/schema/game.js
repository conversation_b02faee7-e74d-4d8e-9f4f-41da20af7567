const Joi = require('joi')

const gameSchema = {
  body: {
    game_name: Joi.string()
      .min(1)
      .max(200)
      .pattern(/^[a-zA-Z0-9_]+$/)
      .required()
      .messages({
        'string.pattern.base': '游戏名只能包含字母、数字和下划线',
        'string.min': '游戏名不能为空',
        'string.max': '游戏名不能超过200个字符'
      }),
    game_desc: Joi.string()
      .min(0)
      .max(300)
      .allow('')
      .messages({
        'string.max': '游戏描述不能超过300个字符'
      }),
    game_id: Joi.number()
      .integer()
      .min(1)
      .optional()
      .messages({
        'number.min': '游戏ID必须为正整数'
      })
  }
}

module.exports = gameSchema