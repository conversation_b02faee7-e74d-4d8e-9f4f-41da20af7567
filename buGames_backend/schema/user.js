const Joi = require('joi')

const userSchema = {
  body: {
    username: Jo<PERSON>.string()
      .pattern(/^[a-zA-Z0-9@._]+$/)
      .min(3)
      .max(50)
      .required()
      .messages({
        'string.pattern.base': '用户名只能包含字母、数字、@、.、_',
        'string.min': '用户名至少需要3个字符',
        'string.max': '用户名不能超过50个字符'
      }),
    password: Joi.string()
      .pattern(/^[a-zA-Z0-9@._!#$%^&*()]+$/)
      .min(6)
      .max(100)
      .required()
      .messages({
        'string.pattern.base': '密码包含不允许的字符',
        'string.min': '密码至少需要6个字符',
        'string.max': '密码不能超过100个字符'
      })
  }
}

module.exports = userSchema