import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/useStore'

// 需要登录才能访问的路由白名单
const authRoutes = ['/new', '/edit']

// 检查是否已登录（包含token有效性验证）
const isAuthenticated = () => {
  const userStore = useUserStore()
  // 每次检查时都验证token有效性
  return userStore.validateToken()
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: () => import('../views/index.vue'),
      children: [
        {
          path: '/',
          component: () => import('../views/components/homeView.vue')
        },
        {
          path: '/Desc/:game_id',
          component: () => import('../views/components/gameDesc.vue')
        },
        {
          path: '/new',
          component: () => import('../views/components/newGame.vue')
        },
        {
          path: '/edit/:game_id',
          component: () => import('../views/components/newGame.vue')
        },
        {
          path: '/login',
          component: () => import('../views/components/gameLogin.vue')
        }
      ]
    },
  ],
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 每次路由切换时都检查token有效性
  userStore.validateToken()

  // 如果访问的是需要认证的路由
  if (authRoutes.includes(to.path)) {
    // 检查是否已登录
    if (!isAuthenticated()) {
      // 未登录则重定向到登录页
      next('/login')
    } else {
      // 已登录则允许访问
      next()
    }
  } else {
    // 不需要认证的路由直接放行
    next()
  }
})

export default router
