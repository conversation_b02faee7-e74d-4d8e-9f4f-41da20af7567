<template>
    <div class="home-view">
        <div class="breadcrumb-container home-breadcrumb">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item>Home</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div>
            <div class="panel">
            <game-item 
            class="item" 
            v-for="item in paginatedGameList" 
            :key="item.game_id" 
            :gameData="item" 
            @click="goToGameDesc(item.game_id)"
            />
            </div>
            <div class="pagination-container">
                <el-pagination
                size="default"
                background
                layout="prev, pager, next"
                :page-size="pageSize"
                :total="total"
                :current-page="currentPage"
                @current-change="handleCurrentChange"
                />
            </div>
        </div>
    </div>

</template>

<script setup>
    import GameItem from './gameItem.vue'
    import { useRouter } from 'vue-router'
    import { ref, onMounted, computed } from 'vue'
    import { getGameListAPI } from '@/utils/request'
    const router = useRouter()
    const goToGameDesc = (gameId) => {
        router.push(`/game/${gameId}`)
    }

    const gameList = ref([])

    const getGameList = async () => {
        try {
            const response = await getGameListAPI()
            if (response && response.data && Array.isArray(response.data)) {
                gameList.value = response.data
                total.value = response.data.length
            } else {
                console.error('Invalid response format:', response)
                gameList.value = []
                total.value = 0
            }
        } catch (error) {
            console.error('Failed to fetch game list:', error)
            gameList.value = []
            total.value = 0
        }
    }
    onMounted(() => {
        getGameList()
    })

    // 分页相关状态
    const currentPage = ref(1)
    const pageSize = ref(9)
    const total = ref(gameList.value.length)

    // 计算当前页显示的游戏列表
    const paginatedGameList = computed(() => {
        if (!Array.isArray(gameList.value)) {
            return []
        }
        const start = (currentPage.value - 1) * pageSize.value
        const end = start + pageSize.value
        return gameList.value.slice(start, end)
    })

    // 处理页码变化
    const handleCurrentChange = (page) => {
        currentPage.value = page
    }

    
    
</script>

<style>
    .home-view {
        background-color: #f0f0f0;
        border: 1px solid #dcdfe6;
        border-top: none;
        border-radius: 0 0 10px 10px;
    }

    .home-breadcrumb {
        width: 96%;
        padding-top: 20px;
    }

    /* .home-breadcrumb .el-breadcrumb__inner {
        margin-left: 15px;
    } */

    .pagination-container {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
        border-top: 1px solid #dcdfe6;
        width: 95%;
        margin: 0 auto;
        padding-top: 20px;
        margin-bottom: 20px;
    }

    .panel {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 20px;
        padding: 20px 50px 15px 50px;
    }

    .panel .item {
        flex: 0 0 calc(33.333% - 13.33px);
        max-width: calc(33.333% - 13.33px);
    }

    .item {
        margin-bottom: 20px;
    }

    /* 响应式设计 - 平板端适配 */
    @media (max-width: 1024px) {
        .panel {
            padding: 20px 30px 15px 30px;
            gap: 15px;
        }

        .panel .item {
            flex: 0 0 calc(50% - 7.5px);
            max-width: calc(50% - 7.5px);
        }
    }

    /* 响应式设计 - 手机端适配 */
    @media (max-width: 768px) {
        .panel {
            padding: 15px 20px 10px 20px;
            gap: 15px;
            justify-content: flex-start;
        }

        .panel .item {
            flex: 0 0 calc(50% - 7.5px);
            max-width: calc(50% - 7.5px);
        }
    }

    /* 小屏手机适配 */
    @media (max-width: 480px) {
        .panel {
            padding: 15px 10px 10px 10px;
            gap: 10px;
        }

        .panel .item {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
</style>