<template>
    <div class="new-game">
        <div class="breadcrumb-container">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }">Home</el-breadcrumb-item>
                <el-breadcrumb-item>{{ formatPath(route.path) }}</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <el-form class="new-game-form" label-width="120px" :rules="rules" ref="formRef" :model="form">
            <el-form-item class="game-name-item" label="Name" size="large" prop="name">
                <el-input
                v-model="form.name"
                clearable
                placeholder="Name"
                @input="handleGameNameInput"
                />
                <span class="remind">accept letters, numbers, underscore only </span>
            </el-form-item>
            <el-form-item label="Describe" size="large" prop="describe">
                <el-input 
                v-model="form.describe"
                type="textarea" 
                :rows="3" 
                maxlength="300"
                show-word-limit
                placeholder="Describe"
                />
            </el-form-item>
            <el-form-item label="File" size="large" prop="file">
                <el-upload
                    class="upload-dragger"
                    drag
                    :auto-upload="false"
                    :show-file-list="true"
                    :on-change="handleFileChange"
                    :on-remove="handleFileRemove"
                    :before-upload="beforeUpload"
                    :limit="1"
                    accept=".html"
                >
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">
                        Drag your files here, or <em>Click Upload</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            Only <span style="color: #409eff;">single html files</span> can be uploaded
                        </div>
                    </template>
                </el-upload>
            </el-form-item>
            <div class="submit-button">
                <el-button type="success" size="large" color="#b0b0b0" @click="handleCancel">Cancel</el-button>
                <el-button type="success" size="large" color="#1f883d" @click="handleSubmit">Submit</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup>
    import { ref, onMounted, computed } from 'vue'
    import { useRouter,useRoute } from 'vue-router'
    import { UploadFilled } from '@element-plus/icons-vue'
    import { ElMessage } from 'element-plus'
    import { addNewGameAPI, editGameAPI, getGameDescAPI } from '@/utils/request'
    import { sanitizeInput, isValidGameName, isValidGameDesc } from '@/utils/security'
    
    const route = useRoute()
    const router = useRouter()
    
    const formatPath = (path) => {
        const pathMap = {
            '/new': 'New Lesson',
            '/edit': 'Edit Lesson'
        }
        return pathMap[path] || path.slice(1)
    }
    
    const formRef = ref(null)
    const form = ref({
        name: '',
        describe: '',
        file: null,
        game_id: null
    })

    // 判断是否为编辑模式
    const isEditMode = computed(() => route.path.includes('/edit'))

    // 动态验证规则
    const rules = computed(() => ({
        name: [
            { required: true, message: 'Please input name', trigger: 'blur' },
            {
                pattern: /^[a-zA-Z0-9_]+$/,
                message: 'Name can only contain letters, numbers and underscores',
                trigger: 'blur'
            },
            {
                min: 1,
                max: 200,
                message: 'Name length should be 1-200 characters',
                trigger: 'blur'
            }
        ],
        describe: [
            { required: false, message: 'Please input describe', trigger: 'blur' }
        ],
        file: [
            { required: !isEditMode.value, message: 'Please upload file', trigger: 'change' }
        ]
    }))

    const beforeUpload = (file) => {
        const isHTML = file.type === 'text/html' || file.name.toLowerCase().endsWith('.html')
        if (!isHTML) {
            ElMessage.error('Only HTML files can be uploaded!')
            return false
        }
        const isLt2M = file.size / 1024 / 1024 < 2
        if (!isLt2M) {
            ElMessage.error('The file size cannot exceed 2MB!')
            return false
        }
        return true
    }

    const handleFileChange = (file, fileList) => {
        if (fileList.length > 0) {
            form.value.file = file.raw
        }
    }

    const handleFileRemove = () => {
        form.value.file = null
    }

    const handleCancel = () => {
        router.push('/')
    }

    // 加载编辑数据
    const loadEditData = async () => {
        if (isEditMode.value) {
            const gameId = route.params.game_id
            if (!gameId) {
                ElMessage.error('Missing game ID')
                router.push('/')
                return
            }

            try {
                const response = await getGameDescAPI(gameId)
                if (response.status === 0) {
                    form.value = {
                        name: response.data.game_name,
                        describe: response.data.game_desc,
                        file: null,
                        game_id: response.data.game_id
                    }
                } else {
                    ElMessage.error(response.message)
                    router.push('/')
                }
            } catch (error) {
                console.error('Load game data failed:', error)
                ElMessage.error('Load game data failed')
                router.push('/')
            }
        }
    }

    const handleSubmit = async () => {
        formRef.value.validate(async (valid) => {
            if (valid) {
                // 安全验证
                if (!isValidGameName(form.value.name)) {
                    ElMessage.error('游戏名称格式不正确')
                    return
                }

                if (!isValidGameDesc(form.value.describe)) {
                    ElMessage.error('游戏描述过长')
                    return
                }

                // 新增模式下必须有文件
                if (!isEditMode.value && !form.value.file) {
                    ElMessage.error('Please select a game file')
                    return
                }

                try {
                    // 创建FormData对象，对输入进行清理
                    const formData = new FormData()
                    formData.append('game_name', sanitizeInput(form.value.name))
                    formData.append('game_desc', sanitizeInput(form.value.describe))

                    if (isEditMode.value) {
                        formData.append('game_id', form.value.game_id)
                    }

                    // 如果有文件则添加文件
                    if (form.value.file) {
                        formData.append('gameFile', form.value.file)
                    }                    

                    // 调用对应的API
                    const response = isEditMode.value
                        ? await editGameAPI(formData)
                        : await addNewGameAPI(formData)

                    if (response.status === 0) {
                        ElMessage.success(response.message)
                        // 重置表单
                        form.value = {
                            name: '',
                            describe: '',
                            file: null,
                            game_id: null
                        }
                        formRef.value.resetFields()
                        // 跳转到首页
                        router.push('/')
                    } else {
                        ElMessage.error(response.message)
                    }
                } catch (error) {
                    console.error('Submit failed:', error)
                    ElMessage.error('Submit failed, please check your network connection')
                }
            }
        })
    }

    // 处理游戏名称输入，只允许字母、数字和下划线
    const handleGameNameInput = (value) => {
        // 首先进行基本的输入清理
        const cleanValue = sanitizeInput(value)
        // 过滤掉不符合规则的字符
        const filteredValue = cleanValue.replace(/[^a-zA-Z0-9_]/g, '')
        form.value.name = filteredValue

        // 实时验证
        if (filteredValue && !isValidGameName(filteredValue)) {
            ElMessage.warning('游戏名称格式不正确')
        }
    }

    // 组件挂载时加载编辑数据
    onMounted(() => {
        loadEditData()
    })
</script>

<style scoped>
    .new-game {
        background-color: #f1f1f1;
        padding: 20px;
        border-radius: 0 0 10px 10px;
        border: 1px solid #dcdfe6;
        border-top: none;
    }

    .submit-button {
        text-align: right;
    }

    .new-game-form {
        padding-top: 20px;
    }

    .upload-dragger {
        width: 100%;
    }

    .el-upload__tip {
        color: #909399;
        font-size: 12px;
        margin-top: 7px;
    }

    .el-icon--upload {
        font-size: 48px;
        color: #c0c4cc;
        margin-bottom: 10px;
    }

    .game-name-item {
        position: relative;
    }

    .remind {
        position: absolute;
        top: 75%;
        left: 15px;
        color: #909399;
        font-size: 12px;
    }
</style>


