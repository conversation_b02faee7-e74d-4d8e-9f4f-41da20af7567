<template>
    <div class="game-desc">
        <div class="breadcrumb-container">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }">Home</el-breadcrumb-item>
                <el-breadcrumb-item>Description</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <h1>Name: <span v-text="game_name"></span></h1>
        <p>Describe: <span v-text="game_desc"></span></p>
        <div class="choice-container">
            <el-button type="success" size="large" color="#4a4a4a" @click="goToHome">Back</el-button>
            <el-button v-if="isLogin" type="success" size="large" color="#6c757d" @click="goToEdit(game_id)">Edit</el-button>
            <el-button v-if="isLogin" type="success" size="large" color="#dc3545" @click="goToDelete">Delete</el-button>
            <el-button type="success" size="large" color="#1f883d" @click="goToGame">Explore</el-button>
        </div>
    </div>
</template>

<script setup>
    import { ElMessage } from 'element-plus'
    import { useUserStore } from '@/stores/useStore'
    import { useRouter, useRoute } from 'vue-router'
    import { computed, ref, onMounted } from 'vue'
    import { deleteGameAPI, getGameDescAPI } from '@/utils/request'

    const game_desc = ref('')

    const game_name = ref('')

    const game_id = ref(0)

    const router = useRouter()
    const route = useRoute()
    const userStore = useUserStore()
    const isLogin = computed(() => userStore.isLogin)

    const getGameDesc = async () => {
        const gameId = route.params.game_id
        const response = await getGameDescAPI(gameId)
        game_name.value = response.data.game_name
        game_desc.value = response.data.game_desc
        game_id.value = response.data.game_id
    }

    onMounted(() => {
        getGameDesc()
    })

    const goToHome = () => {
        router.push('/')
    }
    const goToGame = () => {
        // URL encode the game name to handle spaces and special characters
        const encodedGameName = encodeURIComponent(game_name.value)
        // const Base_URL = 'http://localhost:3001/api'
        const Base_URL = 'https://games.hkbu.tech/api'
        // const Base_URL = 'http://************:3001'
        const gameUrl = Base_URL + '/AccessLessons/' + encodedGameName
        window.open(gameUrl, '_blank')
    }

    const goToEdit = (gameId) => {
        // 这里需要传递游戏ID，暂时使用示例ID
        // 在实际应用中，应该从路由参数或组件props中获取游戏ID
        router.push(`/edit/${gameId}`)
    }

    const goToDelete = () => {
        if(confirm('Please confirm whether to delete')){
            deleteGameAPI(route.params.game_id)
            ElMessage.success({
                message: 'Delete successfully',
                type: 'success',
                duration: 3000
            })
            router.push('/')
        }
    }
</script>

<style>
    .game-desc {
        background-color: #f0f0f0;
        border: 1px solid #dcdfe6;
        border-top: none;
        padding: 20px;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        margin: 0 auto;
    }

    .game-desc h1 {
        font-size: 24px;
        font-weight: bold;
        padding-bottom: 20px;
        border-bottom: 1px solid #dcdfe6;
    }

    .game-desc p {
        font-size: 16px;
        margin-top: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #dcdfe6;
    }

    .choice-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        gap: 20px;
    }

    .choice-container .el-button {
        width: 100px;
    }

    /* .choice-container .el-button:nth-child(2) {
        margin-left: 30px;
    } */
</style>