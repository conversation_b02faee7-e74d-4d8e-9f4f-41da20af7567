<template>
    <el-menu mode="horizontal" class="header-container" text-color="#333">
        <el-button 
        type="success"  
        size="large" 
        color="#fff" 
        class="header-title" 
        @click="goToHome">HKBU Lesson</el-button>
        <div class="header-button-container">
            <el-button 
            v-if="!isLogin"
            type="success" 
            size="small" 
            color="#fff" 
            class="new-game-link" 
            @click="goToLogin">Login</el-button>

            <el-button 
            v-else
            type="success" 
            size="small" 
            color="#fff" 
            class="new-game-link logout-link"
            @click="Logout"
            >Logout</el-button>

            <el-button           
            type="success" 
            size="large" 
            color="#1f883d" 
            class="new-game-link" 
            @click="goToNewGame">New Lesson</el-button>

        </div>
    </el-menu>
</template>

<script setup>
    import { useRouter } from 'vue-router'
    import { useUserStore } from '@/stores/useStore'
    import { computed } from 'vue'

    const router = useRouter()
    const userStore = useUserStore()

    // 使用 computed 来响应式地获取登录状态
    const isLogin = computed(() => userStore.isLogin)

    const goToHome = () => {
        router.push('/')
    }
    const goToNewGame = () => {
        router.push('/new')
    }
    const Logout = () => {
        userStore.Logout()
    }
    const goToLogin = () => {
        router.push('/login')
    }
</script>

<style>
    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        /* background-color: #f8f9fa; */

    }

    .header-container .header-title {
        cursor:default;
        font-size: 22px;
        font-weight: bold;
        color: #333;
        /* background-color: #f8f9fa; */
        /* border: none; */
        margin-left: 20px;
    }

    .header-container .header-title:hover {
        color: #1f883d;
        background-color: #f0f0f0;
    }

    .header-container .new-game-link {
        margin-right: 40px;
        font-size:16px;
    }

    .header-container .header-button-container {
        display: flex;
        align-items: center;
    }

    .header-container .header-button-container .new-game-link {
        margin-right: 10px;
    }

    .header-container .header-button-container .logout-link {
        color: #1f883d;
    }
</style>