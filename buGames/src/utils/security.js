/**
 * 前端安全工具函数
 */

/**
 * 转义HTML特殊字符
 * @param {string} text - 需要转义的文本
 * @returns {string} - 转义后的文本
 */
export const escapeHTML = (text) => {
  if (!text || typeof text !== 'string') {
    return ''
  }
  
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * 清理用户输入，移除潜在的恶意内容
 * @param {string} input - 用户输入
 * @returns {string} - 清理后的输入
 */
export const sanitizeInput = (input) => {
  if (!input || typeof input !== 'string') {
    return ''
  }
  
  // 移除HTML标签
  return input.replace(/<[^>]*>/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+=/gi, '')
    .trim()
}

/**
 * 验证游戏名称格式
 * @param {string} name - 游戏名称
 * @returns {boolean} - 是否有效
 */
export const isValidGameName = (name) => {
  if (!name || typeof name !== 'string') {
    return false
  }
  
  // 只允许字母、数字和下划线
  return /^[a-zA-Z0-9_]+$/.test(name) && name.length >= 1 && name.length <= 200
}

/**
 * 验证游戏描述
 * @param {string} desc - 游戏描述
 * @returns {boolean} - 是否有效
 */
export const isValidGameDesc = (desc) => {
  if (!desc) {
    return true // 描述可以为空
  }
  
  if (typeof desc !== 'string') {
    return false
  }
  
  return desc.length <= 300
}

/**
 * 安全地设置innerHTML
 * @param {HTMLElement} element - DOM元素
 * @param {string} html - HTML内容
 */
export const safeSetInnerHTML = (element, html) => {
  if (!element || !html) {
    return
  }
  
  // 转义HTML内容
  element.textContent = html
}
